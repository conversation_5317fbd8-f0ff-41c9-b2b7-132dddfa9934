#!/usr/bin/env python3
"""
测试表达式解析器
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from optimized_factor_code_generator import ExpressionParser, CommonSubexpressionExtractor, OptimizedFactorInfo

def test_expression_parsing():
    """测试表达式解析"""
    parser = ExpressionParser()
    
    # 测试简单表达式
    test_cases = [
        "Close/ts_Delay(Close,1)-1",
        "ts_Stdev(Close/ts_Delay(Close,1)-1,60)",
        "ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))"
    ]
    
    for formula in test_cases:
        print(f"\n测试公式: {formula}")
        tokens = parser.tokenize(formula)
        print(f"分词结果: {tokens}")
        
        tree = parser.parse_expression(tokens)
        print(f"解析结果: {tree.to_string()}")
        print(f"复杂度: {tree.get_complexity()}")

def test_common_subexpression_extraction():
    """测试公共子表达式提取"""
    formula = "ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))"
    
    print(f"\n测试公共子表达式提取:")
    print(f"公式: {formula}")
    
    factor = OptimizedFactorInfo(21, "p2_et5", formula)
    
    print(f"原始表达式树: {factor.expression_tree.to_string()}")
    print(f"原始复杂度: {factor.expression_tree.get_complexity()}")
    
    print(f"\n公共子表达式:")
    for i, expr in enumerate(factor.common_expressions):
        print(f"  {expr.variable_name}: {expr.to_string()} (使用{expr.usage_count}次, 复杂度{expr.get_complexity()})")
    
    print(f"\n优化后表达式: {factor.optimized_tree.to_string()}")
    print(f"优化后复杂度: {factor.optimized_tree.get_complexity()}")

if __name__ == "__main__":
    test_expression_parsing()
    test_common_subexpression_extraction()
