#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的优化因子代码生成器 - 专注于公共子表达式提取

这个脚本使用简单的字符串匹配来识别公共子表达式，避免复杂的表达式解析。

使用方法：
    python simple_optimized_generator.py --input feature.csv --output_dir optimized_factors

作者：AI Assistant
版本：2.1.0
"""

import sys
import argparse
from typing import List, Dict, Set
from pathlib import Path
import json
import re
from collections import defaultdict


class SimpleFactorInfo:
    """简化的因子信息类"""
    def __init__(self, factor_id: int, factor_name: str, formula: str):
        self.factor_id = factor_id
        self.factor_name = factor_name
        self.formula = formula
        self.required_fields = self._extract_required_fields()
        self.class_name = self._generate_class_name()
        
        # 提取公共子表达式
        self.common_expressions = []
        self.optimized_formula = formula
        self._extract_common_subexpressions()
    
    def _extract_required_fields(self) -> Set[str]:
        """从公式中提取所需的数据字段"""
        market_fields = {'Close', 'Open', 'High', 'Low', 'Volume', 'Amount', 'VWAP'}
        required = set()
        
        for field in market_fields:
            if field in self.formula:
                required.add(field)
        
        return required
    
    def _generate_class_name(self) -> str:
        """生成C++类名"""
        class_name = self.factor_name.replace('_', '')
        class_name = class_name[0].upper() + class_name[1:] if class_name else 'Factor'
        return f"Factor{class_name}"
    
    def _extract_common_subexpressions(self):
        """使用简单的模式匹配提取公共子表达式"""
        # 查找函数调用模式
        function_pattern = r'([a-zA-Z_]+\([^()]*(?:\([^()]*\)[^()]*)*\))'
        
        # 找到所有函数调用
        matches = re.findall(function_pattern, self.formula)
        
        # 统计每个表达式的出现次数
        expression_count = defaultdict(int)
        for match in matches:
            expression_count[match] += 1
        
        # 找出出现多次的表达式
        common_exprs = []
        var_counter = 0
        
        for expr, count in expression_count.items():
            if count >= 2 and len(expr) > 10:  # 只考虑足够长且出现多次的表达式
                var_name = f"p{var_counter}"
                common_exprs.append({
                    'variable': var_name,
                    'expression': expr,
                    'count': count
                })
                var_counter += 1
        
        self.common_expressions = common_exprs
        
        # 生成优化后的公式
        optimized = self.formula
        for expr_info in self.common_expressions:
            optimized = optimized.replace(expr_info['expression'], expr_info['variable'])
        
        self.optimized_formula = optimized


class SimpleCppCodeGenerator:
    """简化的C++代码生成器"""
    
    def __init__(self, output_dir: str):
        self.output_dir = Path(output_dir)
        self.operator_mapping = {
            'ts_Delay': 'feature_operators::ts_Delay',
            'ts_Mean': 'feature_operators::ts_Mean',
            'ts_Stdev': 'feature_operators::ts_Stdev',
            'ts_Delta': 'feature_operators::ts_Delta',
            'ts_Min': 'feature_operators::ts_Min',
            'ts_Max': 'feature_operators::ts_Max',
            'ts_Corr': 'feature_operators::ts_Corr',
            'ts_Sum': 'feature_operators::ts_Sum',
            'ts_Rank': 'feature_operators::ts_Rank',
            'ts_Scale': 'feature_operators::ts_Scale',
            'ts_Regression': 'feature_operators::ts_Regression',
            'pn_Rank': 'feature_operators::pn_Rank',
            'pn_Mean': 'feature_operators::pn_Mean',
            'pn_TransStd': 'feature_operators::pn_TransStd',
            'Tot_Mean': 'feature_operators::Tot_Mean',
            'Tot_Sum': 'feature_operators::Tot_Sum',
            'Tot_Stdev': 'feature_operators::Tot_Stdev',
            'Tot_Rank': 'feature_operators::Tot_Rank',
            'Tot_ArgMax': 'feature_operators::Tot_ArgMax',
            'Tot_ArgMin': 'feature_operators::Tot_ArgMin',
            'IfThen': 'feature_operators::IfThen',
            'Abs': 'feature_operators::Abs',
            'Log': 'feature_operators::Log',
            'Sqrt': 'feature_operators::Sqrt',
            'Equal': 'feature_operators::Equal',
            'getNan': 'feature_operators::getNan',
            'Power': 'feature_operators::Power',
        }
        
        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        (self.output_dir / "include").mkdir(exist_ok=True)
        (self.output_dir / "src").mkdir(exist_ok=True)
    
    def convert_to_cpp(self, formula: str) -> str:
        """将公式转换为C++代码"""
        cpp_code = formula
        
        # 替换操作符
        for py_op, cpp_op in self.operator_mapping.items():
            cpp_code = cpp_code.replace(py_op, cpp_op)
        
        # 替换数据字段访问
        market_fields = ['Close', 'Open', 'High', 'Low', 'Volume', 'Amount', 'VWAP']
        for field in market_fields:
            # 使用正则表达式确保只替换独立的字段名
            pattern = r'\b' + field + r'\b'
            cpp_code = re.sub(pattern, f'get_field(data_map, "{field}")', cpp_code)
        
        return cpp_code
    
    def generate_optimized_calculation(self, factor: SimpleFactorInfo) -> str:
        """生成优化的计算代码"""
        lines = []
        
        # 生成公共子表达式
        for expr_info in factor.common_expressions:
            cpp_expr = self.convert_to_cpp(expr_info['expression'])
            lines.append(f"        auto {expr_info['variable']} = {cpp_expr};")
        
        # 生成最终结果
        final_cpp = self.convert_to_cpp(factor.optimized_formula)
        lines.append(f"        auto result = {final_cpp};")
        
        return '\n'.join(lines)
    
    def generate_factor_implementation(self, factor: SimpleFactorInfo) -> str:
        """生成因子实现文件"""
        calculation_code = self.generate_optimized_calculation(factor)
        required_fields_str = ', '.join(f'"{field}"' for field in factor.required_fields)
        
        # 生成优化信息注释
        optimization_info = []
        if factor.common_expressions:
            optimization_info.append(" * 优化信息:")
            for expr in factor.common_expressions:
                optimization_info.append(f" *   {expr['variable']}: {expr['expression']} (使用{expr['count']}次)")
        
        template = f'''#include "generated_factors/{factor.class_name.lower()}.hpp"
#include <stdexcept>

namespace generated_factors {{

{factor.class_name}::{factor.class_name}(int factor_id, const std::string& factor_name, const std::string& formula)
    : factor_base(factor_id, factor_name, formula) {{
    // 注册因子到管理器
    register_factor();
}}

/**
 * 计算因子值 - 优化版本
 * 原始公式: {factor.formula}
{chr(10).join(optimization_info)}
 */
feature_operators::DataFrame {factor.class_name}::calculate(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) {{

    // 验证输入数据
    if (!validate_input(data_map)) {{
        throw std::runtime_error("Invalid input data for factor " + get_name());
    }}

    try {{
        // 执行优化的因子计算
{calculation_code}
        return result;
    }} catch (const std::exception& e) {{
        throw std::runtime_error("Error calculating factor " + get_name() + ": " + e.what());
    }}
}}

std::vector<std::string> {factor.class_name}::get_required_fields() const {{
    return {{{required_fields_str}}};
}}

}} // namespace generated_factors'''

        return template
    
    def generate_factor_header(self, factor: SimpleFactorInfo) -> str:
        """生成因子头文件"""
        template = f'''#ifndef GENERATED_FACTORS_{factor.class_name.upper()}_HPP
#define GENERATED_FACTORS_{factor.class_name.upper()}_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {{

/**
 * {factor.factor_name} 因子 (优化版本)
 * ID: {factor.factor_id}
 * 原始公式: {factor.formula}
 * 公共子表达式数量: {len(factor.common_expressions)}
 */
class {factor.class_name} : public factor_framework::factor_base {{
public:
    /**
     * 构造函数
     */
    {factor.class_name}(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值 (优化版本)
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
}};

}} // namespace generated_factors

#endif // GENERATED_FACTORS_{factor.class_name.upper()}_HPP'''

        return template
    
    def generate_factors(self, factors: List[SimpleFactorInfo]):
        """生成所有因子代码"""
        print(f"开始生成 {len(factors)} 个优化因子的C++代码...")
        
        # 统计优化效果
        total_common_expressions = 0
        factors_with_optimization = 0
        
        for i, factor in enumerate(factors):
            print(f"生成因子 {i+1}/{len(factors)}: {factor.factor_name}")
            
            common_count = len(factor.common_expressions)
            total_common_expressions += common_count
            
            if common_count > 0:
                factors_with_optimization += 1
                print(f"  优化: 提取了{common_count}个公共子表达式")
            
            # 生成头文件
            header_content = self.generate_factor_header(factor)
            header_file = self.output_dir / "include" / "generated_factors" / f"{factor.class_name.lower()}.hpp"
            header_file.parent.mkdir(parents=True, exist_ok=True)
            header_file.write_text(header_content, encoding='utf-8')
            
            # 生成实现文件
            impl_content = self.generate_factor_implementation(factor)
            impl_file = self.output_dir / "src" / f"{factor.class_name.lower()}.cpp"
            impl_file.write_text(impl_content, encoding='utf-8')
        
        # 输出优化统计
        print(f"\n优化统计:")
        print(f"  总因子数: {len(factors)}")
        print(f"  提取的公共子表达式总数: {total_common_expressions}")
        print(f"  有优化的因子数: {factors_with_optimization}")
        
        print(f"代码生成完成！输出目录: {self.output_dir}")


class SimpleFactorCSVParser:
    """简化的因子CSV文件解析器"""
    
    def __init__(self, csv_file_path: str):
        self.csv_file_path = csv_file_path
    
    def parse(self) -> List[SimpleFactorInfo]:
        """解析CSV文件，返回因子信息列表"""
        factors = []
        
        try:
            with open(self.csv_file_path, 'r', encoding='utf-8') as f:
                # 跳过第一行（标题行）
                next(f)
                
                for line_num, line in enumerate(f, start=2):
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        factor = self._parse_line(line)
                        if factor:
                            factors.append(factor)
                    except Exception as e:
                        print(f"警告：解析第{line_num}行时出错: {e}")
                        continue
        
        except FileNotFoundError:
            raise FileNotFoundError(f"找不到CSV文件: {self.csv_file_path}")
        except Exception as e:
            raise RuntimeError(f"解析CSV文件时出错: {e}")
        
        print(f"成功解析 {len(factors)} 个因子")
        return factors
    
    def _parse_line(self, line: str) -> SimpleFactorInfo:
        """解析CSV行"""
        # 简单的CSV解析（处理带引号的字段）
        parts = []
        current_part = ""
        in_quotes = False
        
        for char in line:
            if char == '"':
                in_quotes = not in_quotes
            elif char == ',' and not in_quotes:
                parts.append(current_part.strip())
                current_part = ""
            else:
                current_part += char
        
        if current_part:
            parts.append(current_part.strip())
        
        if len(parts) < 3:
            raise ValueError(f"CSV行格式错误，期望至少3列: {line}")
        
        # 解析字段
        factor_id = int(parts[0]) if parts[0].isdigit() else 0
        factor_name = parts[1].strip('"')
        formula = parts[2].strip('"')
        
        return SimpleFactorInfo(factor_id, factor_name, formula)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="简化的优化因子代码生成器 - 支持公共子表达式提取",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python simple_optimized_generator.py --input feature.csv --output_dir optimized_factors
  python simple_optimized_generator.py -i feature.csv -o optimized_factors --verbose
        """
    )

    parser.add_argument(
        '--input', '-i',
        required=True,
        help='输入的因子CSV文件路径'
    )

    parser.add_argument(
        '--output_dir', '-o',
        default='optimized_factors',
        help='输出目录路径（默认: optimized_factors）'
    )

    parser.add_argument(
        '--filter', '-f',
        help='因子名称过滤器（支持通配符，如 "p1_*"）'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='启用详细输出'
    )

    parser.add_argument(
        '--dry_run',
        action='store_true',
        help='只解析不生成代码（用于验证）'
    )

    parser.add_argument(
        '--max_factors',
        type=int,
        help='限制生成的因子数量（用于测试）'
    )

    args = parser.parse_args()

    try:
        # 解析CSV文件
        print(f"解析因子配置文件: {args.input}")
        csv_parser = SimpleFactorCSVParser(args.input)
        factors = csv_parser.parse()

        if not factors:
            print("错误：没有找到有效的因子定义")
            return 1

        # 应用过滤器
        if args.filter:
            import fnmatch
            filtered_factors = []
            for factor in factors:
                if fnmatch.fnmatch(factor.factor_name, args.filter):
                    filtered_factors.append(factor)
            factors = filtered_factors
            print(f"应用过滤器 '{args.filter}' 后，剩余 {len(factors)} 个因子")

        # 限制因子数量
        if args.max_factors and len(factors) > args.max_factors:
            factors = factors[:args.max_factors]
            print(f"限制因子数量为 {args.max_factors}")

        # 打印因子信息
        if args.verbose:
            print("\n因子列表:")
            for factor in factors:
                print(f"  ID: {factor.factor_id}, 名称: {factor.factor_name}")
                print(f"    公式: {factor.formula}")
                print(f"    所需字段: {', '.join(factor.required_fields)}")
                print(f"    类名: {factor.class_name}")
                print(f"    公共子表达式数: {len(factor.common_expressions)}")
                if factor.common_expressions:
                    for expr in factor.common_expressions:
                        print(f"      {expr['variable']}: {expr['expression']} (使用{expr['count']}次)")
                print(f"    优化后公式: {factor.optimized_formula}")
                print()

        if args.dry_run:
            print(f"干运行模式：解析了 {len(factors)} 个因子，未生成代码")
            # 输出优化统计
            total_common = sum(len(f.common_expressions) for f in factors)
            factors_with_opt = sum(1 for f in factors if len(f.common_expressions) > 0)
            print(f"优化预览：")
            print(f"  可提取的公共子表达式总数: {total_common}")
            print(f"  有优化潜力的因子数: {factors_with_opt}")
            return 0

        # 生成代码
        print(f"\n开始生成优化代码到目录: {args.output_dir}")
        code_generator = SimpleCppCodeGenerator(args.output_dir)
        code_generator.generate_factors(factors)

        print("\n✅ 优化代码生成完成！")
        print(f"📁 输出目录: {args.output_dir}")
        print(f"📊 生成因子数量: {len(factors)}")

        return 0

    except Exception as e:
        print(f"❌ 错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
