#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于表达式树的因子代码优化器

这个脚本使用正确的表达式树解析和图论算法来识别公共子表达式。

作者：AI Assistant
版本：3.0.0
"""

import sys
import argparse
from typing import List, Dict, Set, Optional, Tuple
from pathlib import Path
import json
from collections import defaultdict
import hashlib


class ASTNode:
    """抽象语法树节点"""
    def __init__(self, node_type: str, value: str, children: List['ASTNode'] = None):
        self.node_type = node_type  # 'function', 'operator', 'variable', 'constant'
        self.value = value
        self.children = children or []
        self._hash = None
        self.variable_name = None
        self.is_common = False
        self.usage_count = 0

    def __hash__(self):
        if self._hash is None:
            # 基于结构和值计算哈希
            content = f"{self.node_type}:{self.value}"
            for child in self.children:
                content += f":{hash(child)}"
            self._hash = hash(content)
        return self._hash

    def __eq__(self, other):
        if not isinstance(other, ASTNode):
            return False
        return (self.node_type == other.node_type and
                self.value == other.value and
                len(self.children) == len(other.children) and
                all(c1 == c2 for c1, c2 in zip(self.children, other.children)))

    def to_string(self) -> str:
        """将AST节点转换为字符串表示"""
        if self.node_type == 'variable' or self.node_type == 'constant':
            return self.value
        elif self.node_type == 'operator':
            if len(self.children) == 2:
                left = self.children[0].to_string()
                right = self.children[1].to_string()
                return f"({left} {self.value} {right})"
            elif len(self.children) == 1:
                operand = self.children[0].to_string()
                return f"({self.value}{operand})"
        elif self.node_type == 'function':
            args = [child.to_string() for child in self.children]
            return f"{self.value}({','.join(args)})"

        return self.value

    def get_complexity(self) -> int:
        """计算子树的复杂度"""
        if self.node_type in ['variable', 'constant']:
            return 1
        return 1 + sum(child.get_complexity() for child in self.children)

    def get_all_subtrees(self) -> List['ASTNode']:
        """获取所有子树（包括自己）"""
        subtrees = [self]
        for child in self.children:
            subtrees.extend(child.get_all_subtrees())
        return subtrees

    def print_tree(self, indent: int = 0, show_hash: bool = False) -> str:
        """打印AST树结构"""
        prefix = "  " * indent
        node_info = f"{prefix}├─ {self.node_type}: {self.value}"

        if show_hash:
            node_info += f" (hash: {hash(self)})"

        if self.is_common:
            node_info += f" [COMMON: {self.variable_name}, used {self.usage_count} times]"

        node_info += f" (complexity: {self.get_complexity()})"

        result = [node_info]

        for i, child in enumerate(self.children):
            if i == len(self.children) - 1:
                # 最后一个子节点
                child_str = child.print_tree(indent + 1, show_hash)
                result.append(child_str)
            else:
                child_str = child.print_tree(indent + 1, show_hash)
                result.append(child_str)

        return '\n'.join(result)

    def get_node_statistics(self) -> Dict[str, int]:
        """获取节点统计信息"""
        stats = defaultdict(int)

        def collect_stats(node):
            stats[f"type_{node.node_type}"] += 1
            stats["total_nodes"] += 1
            if node.is_common:
                stats["common_nodes"] += 1
            for child in node.children:
                collect_stats(child)

        collect_stats(self)
        return dict(stats)


class ImprovedExpressionParser:
    """改进的表达式解析器"""

    def __init__(self):
        self.functions = {
            'ts_Delay', 'ts_Mean', 'ts_Stdev', 'ts_Delta', 'ts_Min', 'ts_Max',
            'ts_Corr', 'ts_Sum', 'ts_Rank', 'ts_Scale', 'ts_Regression',
            'pn_Rank', 'pn_Mean', 'pn_TransStd',
            'Tot_Mean', 'Tot_Sum', 'Tot_Stdev', 'Tot_Rank', 'Tot_ArgMax', 'Tot_ArgMin',
            'IfThen', 'Abs', 'Log', 'Sqrt', 'Equal', 'getNan', 'Power'
        }

        self.operators = {
            '+': 1, '-': 1,
            '*': 2, '/': 2,
        }

        self.market_fields = {'Close', 'Open', 'High', 'Low', 'Volume', 'Amount', 'VWAP'}

    def tokenize(self, formula: str) -> List[str]:
        """改进的分词器"""
        tokens = []
        i = 0
        while i < len(formula):
            if formula[i].isspace():
                i += 1
                continue

            # 处理函数名和变量名
            if formula[i].isalpha() or formula[i] == '_':
                j = i
                while j < len(formula) and (formula[j].isalnum() or formula[j] == '_'):
                    j += 1
                tokens.append(formula[i:j])
                i = j
            # 处理数字（包括小数）
            elif formula[i].isdigit() or formula[i] == '.':
                j = i
                while j < len(formula) and (formula[j].isdigit() or formula[j] == '.'):
                    j += 1
                tokens.append(formula[i:j])
                i = j
            # 处理操作符和分隔符
            elif formula[i] in '+-*/(),':
                tokens.append(formula[i])
                i += 1
            else:
                i += 1

        return tokens

    def parse(self, formula: str) -> ASTNode:
        """解析公式为AST"""
        tokens = self.tokenize(formula)
        result, _ = self._parse_expression(tokens, 0)
        return result

    def _parse_expression(self, tokens: List[str], pos: int) -> Tuple[ASTNode, int]:
        """解析表达式"""
        return self._parse_additive(tokens, pos)

    def _parse_additive(self, tokens: List[str], pos: int) -> Tuple[ASTNode, int]:
        """解析加减表达式"""
        left, pos = self._parse_multiplicative(tokens, pos)

        while pos < len(tokens) and tokens[pos] in ['+', '-']:
            op = tokens[pos]
            pos += 1
            right, pos = self._parse_multiplicative(tokens, pos)
            left = ASTNode('operator', op, [left, right])

        return left, pos

    def _parse_multiplicative(self, tokens: List[str], pos: int) -> Tuple[ASTNode, int]:
        """解析乘除表达式"""
        left, pos = self._parse_primary(tokens, pos)

        while pos < len(tokens) and tokens[pos] in ['*', '/']:
            op = tokens[pos]
            pos += 1
            right, pos = self._parse_primary(tokens, pos)
            left = ASTNode('operator', op, [left, right])

        return left, pos

    def _parse_primary(self, tokens: List[str], pos: int) -> Tuple[ASTNode, int]:
        """解析基本表达式"""
        if pos >= len(tokens):
            raise ValueError("Unexpected end of expression")

        token = tokens[pos]

        # 处理括号
        if token == '(':
            pos += 1
            expr, pos = self._parse_expression(tokens, pos)
            if pos >= len(tokens) or tokens[pos] != ')':
                raise ValueError("Missing closing parenthesis")
            pos += 1
            return expr, pos

        # 处理函数调用
        if token in self.functions:
            return self._parse_function_call(tokens, pos)

        # 处理变量和常量
        if token in self.market_fields:
            return ASTNode('variable', token), pos + 1
        elif token.replace('.', '').isdigit():
            return ASTNode('constant', token), pos + 1
        else:
            return ASTNode('variable', token), pos + 1

    def _parse_function_call(self, tokens: List[str], pos: int) -> Tuple[ASTNode, int]:
        """解析函数调用"""
        func_name = tokens[pos]
        pos += 1

        if pos >= len(tokens) or tokens[pos] != '(':
            raise ValueError(f"Expected '(' after function name {func_name}")

        pos += 1  # 跳过 '('
        args = []

        # 解析参数
        while pos < len(tokens) and tokens[pos] != ')':
            if tokens[pos] == ',':
                pos += 1
                continue

            arg, pos = self._parse_expression(tokens, pos)
            args.append(arg)

        if pos >= len(tokens) or tokens[pos] != ')':
            raise ValueError("Missing closing parenthesis for function call")

        pos += 1  # 跳过 ')'
        return ASTNode('function', func_name, args), pos


class TreeBasedSubexpressionExtractor:
    """基于树结构的公共子表达式提取器"""

    def __init__(self, min_complexity: int = 3, min_usage: int = 2):
        self.min_complexity = min_complexity
        self.min_usage = min_usage
        self.subtree_count = defaultdict(int)
        self.subtree_nodes = {}
        self.variable_counter = 0

    def extract_common_subexpressions(self, ast: ASTNode) -> List[ASTNode]:
        """提取公共子表达式"""
        # 记录根节点，避免将整个表达式识别为公共子表达式
        root_hash = hash(ast)

        # 收集所有子树
        self._collect_subtrees(ast)

        # 识别公共子表达式，但排除根节点
        common_expressions = []
        for subtree_hash, count in self.subtree_count.items():
            if (count >= self.min_usage and
                subtree_hash != root_hash):  # 排除根节点
                node = self.subtree_nodes[subtree_hash]
                if node.get_complexity() >= self.min_complexity:
                    node.is_common = True
                    node.usage_count = count
                    node.variable_name = f"p{self.variable_counter}"
                    self.variable_counter += 1
                    common_expressions.append(node)

        # 按复杂度排序，简单的先提取（确保依赖关系正确）
        common_expressions.sort(key=lambda x: x.get_complexity())

        # 重新分配变量名，确保依赖顺序
        for i, expr in enumerate(common_expressions):
            expr.variable_name = f"p{i}"

        return common_expressions

    def _collect_subtrees(self, node: ASTNode):
        """递归收集所有子树"""
        # 先递归处理子节点
        for child in node.children:
            self._collect_subtrees(child)

        # 只收集函数调用和复杂表达式，但排除根节点（整个表达式）
        if (node.get_complexity() >= self.min_complexity and
            (node.node_type == 'function' or
             (node.node_type == 'operator' and len(node.children) > 0))):
            node_hash = hash(node)
            self.subtree_count[node_hash] += 1
            self.subtree_nodes[node_hash] = node

    def replace_with_variables(self, ast: ASTNode, common_map: Dict[int, str]) -> ASTNode:
        """用变量替换公共子表达式"""
        node_hash = hash(ast)

        # 如果当前节点是公共表达式，替换为变量
        if node_hash in common_map:
            return ASTNode('variable', common_map[node_hash])

        # 递归处理子节点
        new_children = []
        for child in ast.children:
            new_child = self.replace_with_variables(child, common_map)
            new_children.append(new_child)

        # 创建新节点
        new_node = ASTNode(ast.node_type, ast.value, new_children)
        return new_node


class TreeBasedFactorInfo:
    """基于树结构的因子信息类"""
    def __init__(self, factor_id: int, factor_name: str, formula: str):
        self.factor_id = factor_id
        self.factor_name = factor_name
        self.formula = formula
        self.required_fields = self._extract_required_fields()
        self.class_name = self._generate_class_name()

        # 解析和优化
        self.parser = ImprovedExpressionParser()
        self.extractor = TreeBasedSubexpressionExtractor()
        self.ast = None
        self.common_expressions = []
        self.optimized_ast = None
        self._parse_and_optimize()

    def _extract_required_fields(self) -> Set[str]:
        """从公式中提取所需的数据字段"""
        market_fields = {'Close', 'Open', 'High', 'Low', 'Volume', 'Amount', 'VWAP'}
        required = set()

        for field in market_fields:
            if field in self.formula:
                required.add(field)

        return required

    def _generate_class_name(self) -> str:
        """生成C++类名"""
        class_name = self.factor_name.replace('_', '')
        class_name = class_name[0].upper() + class_name[1:] if class_name else 'Factor'
        return f"Factor{class_name}"

    def _parse_and_optimize(self):
        """解析和优化表达式"""
        try:
            # 解析为AST
            self.ast = self.parser.parse(self.formula)

            # 提取公共子表达式
            self.common_expressions = self.extractor.extract_common_subexpressions(self.ast)

            # 创建替换映射
            common_map = {}
            for expr in self.common_expressions:
                expr_hash = hash(expr)
                common_map[expr_hash] = expr.variable_name

            # 替换公共子表达式
            self.optimized_ast = self.extractor.replace_with_variables(self.ast, common_map)

        except Exception as e:
            print(f"解析公式失败: {self.formula}, 错误: {e}")
            # 创建一个简单的AST作为fallback
            self.ast = ASTNode('variable', 'ERROR')
            self.common_expressions = []
            self.optimized_ast = self.ast


def test_tree_based_parser():
    """测试基于树的解析器"""
    parser = ImprovedExpressionParser()

    test_cases = [
        "Close/ts_Delay(Close,1)-1",
        "ts_Stdev(Close/ts_Delay(Close,1)-1,60)",
        "ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))"
    ]

    for formula in test_cases:
        print(f"\n测试公式: {formula}")
        try:
            ast = parser.parse(formula)
            print(f"解析结果: {ast.to_string()}")
            print(f"复杂度: {ast.get_complexity()}")

            # 测试子表达式提取
            extractor = TreeBasedSubexpressionExtractor()
            common_exprs = extractor.extract_common_subexpressions(ast)
            print(f"公共子表达式数量: {len(common_exprs)}")

        except Exception as e:
            print(f"解析失败: {e}")


def test_factor_optimization():
    """测试因子优化"""
    formula = "ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))"

    print(f"\n测试因子优化:")
    print(f"公式: {formula}")

    factor = TreeBasedFactorInfo(21, "p2_et5", formula)

    if factor.ast:
        print(f"原始AST: {factor.ast.to_string()}")
        print(f"原始复杂度: {factor.ast.get_complexity()}")

        print(f"\n公共子表达式:")
        for expr in factor.common_expressions:
            print(f"  {expr.variable_name}: {expr.to_string()} (使用{expr.usage_count}次, 复杂度{expr.get_complexity()})")

        print(f"\n优化后AST: {factor.optimized_ast.to_string()}")
        print(f"优化后复杂度: {factor.optimized_ast.get_complexity()}")


class TreeBasedCppCodeGenerator:
    """基于树结构的C++代码生成器"""

    def __init__(self, output_dir: str):
        self.output_dir = Path(output_dir)
        self.operator_mapping = {
            'ts_Delay': 'feature_operators::ts_Delay',
            'ts_Mean': 'feature_operators::ts_Mean',
            'ts_Stdev': 'feature_operators::ts_Stdev',
            'ts_Delta': 'feature_operators::ts_Delta',
            'ts_Min': 'feature_operators::ts_Min',
            'ts_Max': 'feature_operators::ts_Max',
            'ts_Corr': 'feature_operators::ts_Corr',
            'ts_Sum': 'feature_operators::ts_Sum',
            'ts_Rank': 'feature_operators::ts_Rank',
            'ts_Scale': 'feature_operators::ts_Scale',
            'ts_Regression': 'feature_operators::ts_Regression',
            'pn_Rank': 'feature_operators::pn_Rank',
            'pn_Mean': 'feature_operators::pn_Mean',
            'pn_TransStd': 'feature_operators::pn_TransStd',
            'Tot_Mean': 'feature_operators::Tot_Mean',
            'Tot_Sum': 'feature_operators::Tot_Sum',
            'Tot_Stdev': 'feature_operators::Tot_Stdev',
            'Tot_Rank': 'feature_operators::Tot_Rank',
            'Tot_ArgMax': 'feature_operators::Tot_ArgMax',
            'Tot_ArgMin': 'feature_operators::Tot_ArgMin',
            'IfThen': 'feature_operators::IfThen',
            'Abs': 'feature_operators::Abs',
            'Log': 'feature_operators::Log',
            'Sqrt': 'feature_operators::Sqrt',
            'Equal': 'feature_operators::Equal',
            'getNan': 'feature_operators::getNan',
            'Power': 'feature_operators::Power',
        }

        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        (self.output_dir / "include").mkdir(exist_ok=True)
        (self.output_dir / "src").mkdir(exist_ok=True)

    def ast_to_cpp(self, node: ASTNode) -> str:
        """将AST节点转换为C++代码"""
        if node.node_type == 'variable':
            if node.value in ['Close', 'Open', 'High', 'Low', 'Volume', 'Amount', 'VWAP']:
                return f'get_field(data_map, "{node.value}")'
            return node.value
        elif node.node_type == 'constant':
            return node.value
        elif node.node_type == 'operator':
            if len(node.children) == 2:
                left = self.ast_to_cpp(node.children[0])
                right = self.ast_to_cpp(node.children[1])
                return f"({left} {node.value} {right})"
            elif len(node.children) == 1:
                operand = self.ast_to_cpp(node.children[0])
                return f"({node.value}{operand})"
        elif node.node_type == 'function':
            cpp_func = self.operator_mapping.get(node.value, node.value)
            args = [self.ast_to_cpp(child) for child in node.children]
            return f"{cpp_func}({','.join(args)})"

        return node.value

    def generate_optimized_calculation(self, factor: TreeBasedFactorInfo) -> str:
        """生成优化的计算代码"""
        lines = []

        # 分析哪些公共子表达式实际被使用
        used_expressions = self._analyze_used_expressions(factor)

        # 创建变量映射
        common_map = {}

        # 只生成实际被使用的公共子表达式
        for expr in used_expressions:
            # 为当前表达式创建一个副本，并替换其中的子表达式
            expr_copy = self._copy_ast_with_replacement(expr, common_map)
            cpp_code = self.ast_to_cpp(expr_copy)
            lines.append(f"        auto {expr.variable_name} = {cpp_code};")

            # 将当前变量加入映射
            expr_hash = hash(expr)
            common_map[expr_hash] = expr.variable_name

        # 生成最终结果
        final_code = self.ast_to_cpp(factor.optimized_ast)
        lines.append(f"        auto result = {final_code};")

        return '\n'.join(lines)

    def _analyze_used_expressions(self, factor: TreeBasedFactorInfo) -> List[ASTNode]:
        """分析哪些公共子表达式实际被使用（只返回最终表达式中直接使用的）"""
        used_variables = set()

        # 从最终表达式开始，递归收集所有被使用的变量
        self._collect_used_variables(factor.optimized_ast, used_variables)

        # 只返回最终表达式中直接使用的公共子表达式
        used_expressions = []
        for expr in factor.common_expressions:
            if expr.variable_name in used_variables:
                used_expressions.append(expr)

        return used_expressions

    def _collect_used_variables(self, node: ASTNode, used_variables: set):
        """递归收集被使用的变量名"""
        # 如果是变量节点且是公共子表达式变量
        if node.node_type == 'variable' and node.value.startswith('p'):
            used_variables.add(node.value)

        # 递归处理子节点
        for child in node.children:
            self._collect_used_variables(child, used_variables)

    def _copy_ast_with_replacement(self, node: ASTNode, common_map: Dict[int, str]) -> ASTNode:
        """复制AST节点，并替换其中的公共子表达式"""
        node_hash = hash(node)

        # 如果当前节点在映射中，替换为变量
        if node_hash in common_map:
            return ASTNode('variable', common_map[node_hash])

        # 递归处理子节点
        new_children = []
        for child in node.children:
            new_child = self._copy_ast_with_replacement(child, common_map)
            new_children.append(new_child)

        return ASTNode(node.node_type, node.value, new_children)

    def generate_factor_implementation(self, factor: TreeBasedFactorInfo) -> str:
        """生成因子实现文件"""
        calculation_code = self.generate_optimized_calculation(factor)
        required_fields_str = ', '.join(f'"{field}"' for field in factor.required_fields)

        # 生成优化信息注释
        optimization_info = []
        if factor.common_expressions:
            optimization_info.append(" * 优化信息:")
            for expr in factor.common_expressions:
                optimization_info.append(f" *   {expr.variable_name}: {expr.to_string()} (使用{expr.usage_count}次)")

        template = f'''#include "generated_factors/{factor.class_name.lower()}.hpp"
#include <stdexcept>

namespace generated_factors {{

{factor.class_name}::{factor.class_name}(int factor_id, const std::string& factor_name, const std::string& formula)
    : factor_base(factor_id, factor_name, formula) {{
    // 注册因子到管理器
    register_factor();
}}

/**
 * 计算因子值 - 基于树结构的优化版本
 * 原始公式: {factor.formula}
{chr(10).join(optimization_info)}
 */
feature_operators::DataFrame {factor.class_name}::calculate(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) {{

    // 验证输入数据
    if (!validate_input(data_map)) {{
        throw std::runtime_error("Invalid input data for factor " + get_name());
    }}

    try {{
        // 执行基于树结构优化的因子计算
{calculation_code}
        return result;
    }} catch (const std::exception& e) {{
        throw std::runtime_error("Error calculating factor " + get_name() + ": " + e.what());
    }}
}}

std::vector<std::string> {factor.class_name}::get_required_fields() const {{
    return {{{required_fields_str}}};
}}

}} // namespace generated_factors'''

        return template

    def generate_factor_header(self, factor: TreeBasedFactorInfo) -> str:
        """生成因子头文件"""
        template = f'''#ifndef GENERATED_FACTORS_{factor.class_name.upper()}_HPP
#define GENERATED_FACTORS_{factor.class_name.upper()}_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {{

/**
 * {factor.factor_name} 因子 (基于树结构优化)
 * ID: {factor.factor_id}
 * 原始公式: {factor.formula}
 * 公共子表达式数量: {len(factor.common_expressions)}
 * 算法: 基于AST的公共子表达式提取
 */
class {factor.class_name} : public factor_framework::factor_base {{
public:
    /**
     * 构造函数
     */
    {factor.class_name}(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值 (树结构优化版本)
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
}};

}} // namespace generated_factors

#endif // GENERATED_FACTORS_{factor.class_name.upper()}_HPP'''

        return template

    def generate_factors(self, factors: List[TreeBasedFactorInfo]):
        """生成所有因子代码"""
        print(f"开始生成 {len(factors)} 个基于树结构优化的因子C++代码...")

        # 统计优化效果
        total_original_complexity = 0
        total_optimized_complexity = 0
        total_common_expressions = 0
        factors_with_optimization = 0

        for i, factor in enumerate(factors):
            print(f"生成因子 {i+1}/{len(factors)}: {factor.factor_name}")

            if factor.ast:
                # 统计复杂度
                original_complexity = factor.ast.get_complexity()
                optimized_complexity = factor.optimized_ast.get_complexity()
                common_count = len(factor.common_expressions)

                total_original_complexity += original_complexity
                total_optimized_complexity += optimized_complexity + sum(expr.get_complexity() for expr in factor.common_expressions)
                total_common_expressions += common_count

                if common_count > 0:
                    factors_with_optimization += 1
                    print(f"  优化: 提取了{common_count}个公共子表达式")
                    print(f"  复杂度: {original_complexity} -> {optimized_complexity} (主表达式)")

            # 生成头文件
            header_content = self.generate_factor_header(factor)
            header_file = self.output_dir / "include" / "generated_factors" / f"{factor.class_name.lower()}.hpp"
            header_file.parent.mkdir(parents=True, exist_ok=True)
            header_file.write_text(header_content, encoding='utf-8')

            # 生成实现文件
            impl_content = self.generate_factor_implementation(factor)
            impl_file = self.output_dir / "src" / f"{factor.class_name.lower()}.cpp"
            impl_file.write_text(impl_content, encoding='utf-8')

        # 输出优化统计
        print(f"\n=== 基于树结构的优化统计 ===")
        print(f"  总因子数: {len(factors)}")
        print(f"  提取的公共子表达式总数: {total_common_expressions}")
        print(f"  有优化的因子数: {factors_with_optimization}")
        print(f"  原始总复杂度: {total_original_complexity}")
        print(f"  优化后总复杂度: {total_optimized_complexity}")
        if total_original_complexity > 0:
            print(f"  优化率: {factors_with_optimization/len(factors)*100:.1f}%")

        # 生成其他文件
        self.generate_all_factors_header(factors)
        self.generate_cmake_file(factors)
        self.generate_optimization_report(factors)

        print(f"代码生成完成！输出目录: {self.output_dir}")

    def generate_all_factors_header(self, factors: List[TreeBasedFactorInfo]):
        """生成包含所有因子的头文件"""
        includes = []
        for factor in factors:
            includes.append(f'#include "generated_factors/{factor.class_name.lower()}.hpp"')

        template = f'''#ifndef GENERATED_FACTORS_ALL_FACTORS_HPP
#define GENERATED_FACTORS_ALL_FACTORS_HPP

/**
 * 基于树结构优化的因子头文件
 * 包含所有从feature.csv生成的优化因子类
 *
 * 生成的因子数量: {len(factors)}
 * 优化算法: 基于AST的公共子表达式提取
 * 理论基础: 编译原理 + 图论
 */

// 包含因子框架
#include "factor_framework/factor_framework.hpp"

// 包含所有生成的优化因子
{chr(10).join(includes)}

namespace generated_factors {{

/**
 * 获取所有生成的因子名称
 */
std::vector<std::string> get_all_generated_factor_names();

/**
 * 获取所有生成的因子ID
 */
std::vector<int> get_all_generated_factor_ids();

/**
 * 初始化所有生成的因子（确保注册）
 */
void initialize_all_factors();

}} // namespace generated_factors

#endif // GENERATED_FACTORS_ALL_FACTORS_HPP'''

        all_header_file = self.output_dir / "include" / "generated_factors" / "all_factors.hpp"
        all_header_file.write_text(template, encoding='utf-8')

    def generate_cmake_file(self, factors: List[TreeBasedFactorInfo]):
        """生成CMake构建文件"""
        source_files = []
        for factor in factors:
            source_files.append(f"src/{factor.class_name.lower()}.cpp")

        template = f'''# 基于树结构优化的因子CMake文件
# 算法: AST + 公共子表达式提取
cmake_minimum_required(VERSION 3.10)

# 添加生成的优化因子源文件
set(TREE_OPTIMIZED_FACTOR_SOURCES
{chr(10).join(f"    {src}" for src in source_files)}
)

# 添加生成的因子头文件目录
set(TREE_OPTIMIZED_FACTOR_INCLUDE_DIRS
    include
)

# 创建基于树结构优化的因子库
add_library(tree_optimized_factors STATIC ${{TREE_OPTIMIZED_FACTOR_SOURCES}})

# 设置包含目录
target_include_directories(tree_optimized_factors PUBLIC
    ${{TREE_OPTIMIZED_FACTOR_INCLUDE_DIRS}}
    ${{CMAKE_SOURCE_DIR}}/include
)

# 链接feature_operators库
target_link_libraries(tree_optimized_factors
    feature_ops_lib
)

# 设置编译选项
target_compile_features(tree_optimized_factors PUBLIC cxx_std_17)
target_compile_options(tree_optimized_factors PRIVATE
    $<$<CXX_COMPILER_ID:GNU>:-Wall -Wextra -O3>
    $<$<CXX_COMPILER_ID:Clang>:-Wall -Wextra -O3>
    $<$<CXX_COMPILER_ID:MSVC>:/W4 /O2>
)

# 安装规则
install(TARGETS tree_optimized_factors
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(DIRECTORY include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.hpp"
)'''

        cmake_file = self.output_dir / "CMakeLists.txt"
        cmake_file.write_text(template, encoding='utf-8')

    def generate_optimization_report(self, factors: List[TreeBasedFactorInfo]):
        """生成优化报告"""
        report_data = []
        total_original = 0
        total_optimized = 0
        total_common = 0

        for factor in factors:
            if factor.ast:
                original_complexity = factor.ast.get_complexity()
                optimized_complexity = factor.optimized_ast.get_complexity()
                common_count = len(factor.common_expressions)

                total_original += original_complexity
                total_optimized += optimized_complexity
                total_common += common_count

                factor_data = {
                    "id": factor.factor_id,
                    "name": factor.factor_name,
                    "formula": factor.formula,
                    "original_complexity": original_complexity,
                    "optimized_main_complexity": optimized_complexity,
                    "common_expressions_count": common_count,
                    "common_expressions": [
                        {
                            "variable": expr.variable_name,
                            "expression": expr.to_string(),
                            "usage_count": expr.usage_count,
                            "complexity": expr.get_complexity()
                        }
                        for expr in factor.common_expressions
                    ]
                }
                report_data.append(factor_data)

        # 生成JSON报告
        report = {
            "algorithm": "Tree-based Common Subexpression Extraction",
            "theory_basis": ["Abstract Syntax Tree", "Graph Theory", "Compiler Design"],
            "summary": {
                "total_factors": len(factors),
                "total_original_complexity": total_original,
                "total_optimized_main_complexity": total_optimized,
                "total_common_expressions": total_common,
                "factors_with_optimization": sum(1 for f in factors if len(f.common_expressions) > 0),
            },
            "factors": report_data
        }

        report_file = self.output_dir / "tree_optimization_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)


class TreeBasedFactorCSVParser:
    """基于树结构的因子CSV文件解析器"""

    def __init__(self, csv_file_path: str):
        self.csv_file_path = csv_file_path

    def parse(self) -> List[TreeBasedFactorInfo]:
        """解析CSV文件，返回因子信息列表"""
        factors = []

        try:
            with open(self.csv_file_path, 'r', encoding='utf-8') as f:
                # 跳过第一行（标题行）
                next(f)

                for line_num, line in enumerate(f, start=2):
                    line = line.strip()
                    if not line:
                        continue

                    try:
                        factor = self._parse_line(line)
                        if factor:
                            factors.append(factor)
                    except Exception as e:
                        print(f"警告：解析第{line_num}行时出错: {e}")
                        continue

        except FileNotFoundError:
            raise FileNotFoundError(f"找不到CSV文件: {self.csv_file_path}")
        except Exception as e:
            raise RuntimeError(f"解析CSV文件时出错: {e}")

        print(f"成功解析 {len(factors)} 个因子")
        return factors

    def _parse_line(self, line: str) -> TreeBasedFactorInfo:
        """解析CSV行"""
        # 简单的CSV解析（处理带引号的字段）
        parts = []
        current_part = ""
        in_quotes = False

        for char in line:
            if char == '"':
                in_quotes = not in_quotes
            elif char == ',' and not in_quotes:
                parts.append(current_part.strip())
                current_part = ""
            else:
                current_part += char

        if current_part:
            parts.append(current_part.strip())

        if len(parts) < 3:
            raise ValueError(f"CSV行格式错误，期望至少3列: {line}")

        # 解析字段
        factor_id = int(parts[0]) if parts[0].isdigit() else 0
        factor_name = parts[1].strip('"')
        formula = parts[2].strip('"')

        return TreeBasedFactorInfo(factor_id, factor_name, formula)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="基于树结构的优化因子代码生成器 - 使用AST和图论算法",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 运行测试
  python tree_based_optimizer.py --test

  # 测试单个公式
  python tree_based_optimizer.py --formula "ts_Stdev(Close/ts_Delay(Close,1)-1,60)-pn_Mean(ts_Stdev(Close/ts_Delay(Close,1)-1,60))"

  # 从CSV生成优化代码
  python tree_based_optimizer.py --input feature.csv --output_dir tree_optimized_factors

  # 生成特定因子
  python tree_based_optimizer.py --input feature.csv --filter "p2_et5" --output_dir tree_optimized_factors --verbose

  # 预览优化效果
  python tree_based_optimizer.py --input feature.csv --filter "p2_et*" --dry_run --verbose

算法特点:
  - 基于抽象语法树(AST)的表达式解析
  - 使用图论算法识别公共子表达式
  - 递归下降解析器处理复杂嵌套表达式
  - 结构化哈希确保精确匹配
        """
    )

    parser.add_argument(
        '--test',
        action='store_true',
        help='运行算法测试'
    )

    parser.add_argument(
        '--formula',
        help='测试单个公式的优化效果'
    )

    parser.add_argument(
        '--input', '-i',
        help='输入的因子CSV文件路径'
    )

    parser.add_argument(
        '--output_dir', '-o',
        default='tree_optimized_factors',
        help='输出目录路径（默认: tree_optimized_factors）'
    )

    parser.add_argument(
        '--filter', '-f',
        help='因子名称过滤器（支持通配符，如 "p2_et*"）'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='启用详细输出'
    )

    parser.add_argument(
        '--dry_run',
        action='store_true',
        help='只解析不生成代码（用于验证优化效果）'
    )

    parser.add_argument(
        '--max_factors',
        type=int,
        help='限制生成的因子数量（用于测试）'
    )

    parser.add_argument(
        '--min_complexity',
        type=int,
        default=3,
        help='公共子表达式的最小复杂度阈值（默认: 3）'
    )

    parser.add_argument(
        '--min_usage',
        type=int,
        default=2,
        help='公共子表达式的最小使用次数阈值（默认: 2）'
    )

    parser.add_argument(
        '--debug',
        action='store_true',
        help='启用调试模式，显示AST树结构和详细分析'
    )

    parser.add_argument(
        '--show_tree',
        action='store_true',
        help='显示AST树的可视化结构'
    )

    parser.add_argument(
        '--show_hash',
        action='store_true',
        help='在树结构中显示节点哈希值'
    )

    parser.add_argument(
        '--analyze_only',
        action='store_true',
        help='只进行分析，不生成代码（比dry_run更详细）'
    )

    args = parser.parse_args()

    try:
        if args.test:
            print("=== 运行基于树结构的算法测试 ===")
            test_tree_based_parser()
            test_factor_optimization()

        elif args.formula:
            print(f"=== 测试单个公式优化 ===")
            print(f"公式: {args.formula}")
            factor = TreeBasedFactorInfo(1, "test", args.formula)

            if factor.ast:
                print(f"原始AST: {factor.ast.to_string()}")
                print(f"原始复杂度: {factor.ast.get_complexity()}")

                # 显示AST树结构
                if args.show_tree or args.debug:
                    print(f"\n=== 原始AST树结构 ===")
                    print(factor.ast.print_tree(show_hash=args.show_hash))

                # 显示节点统计
                if args.debug:
                    stats = factor.ast.get_node_statistics()
                    print(f"\n=== AST节点统计 ===")
                    for key, value in stats.items():
                        print(f"  {key}: {value}")

                print(f"\n=== 公共子表达式分析 ===")
                print(f"识别的公共子表达式:")
                for expr in factor.common_expressions:
                    print(f"  {expr.variable_name}: {expr.to_string()} (使用{expr.usage_count}次, 复杂度{expr.get_complexity()})")

                # 显示公共子表达式的树结构
                if args.debug and factor.common_expressions:
                    print(f"\n=== 公共子表达式树结构 ===")
                    for expr in factor.common_expressions:
                        print(f"\n{expr.variable_name}:")
                        print(expr.print_tree(show_hash=args.show_hash))

                print(f"\n=== 优化结果 ===")
                print(f"优化后AST: {factor.optimized_ast.to_string()}")

                # 显示优化后的树结构
                if args.show_tree or args.debug:
                    print(f"\n=== 优化后AST树结构 ===")
                    print(factor.optimized_ast.print_tree(show_hash=args.show_hash))

                # 分析使用的表达式
                if args.debug:
                    generator = TreeBasedCppCodeGenerator("test_output")
                    used_expressions = generator._analyze_used_expressions(factor)
                    print(f"\n=== 使用分析 ===")
                    print(f"实际使用的公共子表达式:")
                    for expr in used_expressions:
                        print(f"  {expr.variable_name}: {expr.to_string()}")

                # 生成C++代码
                generator = TreeBasedCppCodeGenerator("test_output")
                cpp_code = generator.generate_optimized_calculation(factor)
                print(f"\n=== 生成的C++代码 ===")
                print(cpp_code)

        elif args.input:
            print(f"=== 基于树结构的批量因子优化 ===")
            print(f"解析因子配置文件: {args.input}")

            # 解析CSV文件
            csv_parser = TreeBasedFactorCSVParser(args.input)
            factors = csv_parser.parse()

            if not factors:
                print("错误：没有找到有效的因子定义")
                return 1

            # 应用过滤器
            if args.filter:
                import fnmatch
                filtered_factors = []
                for factor in factors:
                    if fnmatch.fnmatch(factor.factor_name, args.filter):
                        filtered_factors.append(factor)
                factors = filtered_factors
                print(f"应用过滤器 '{args.filter}' 后，剩余 {len(factors)} 个因子")

            # 限制因子数量
            if args.max_factors and len(factors) > args.max_factors:
                factors = factors[:args.max_factors]
                print(f"限制因子数量为 {args.max_factors}")

            # 设置优化参数
            for factor in factors:
                factor.extractor.min_complexity = args.min_complexity
                factor.extractor.min_usage = args.min_usage
                # 重新解析和优化
                factor._parse_and_optimize()

            # 打印因子信息
            if args.verbose:
                print("\n=== 因子优化详情 ===")
                for factor in factors:
                    if factor.ast:
                        print(f"\n因子: {factor.factor_name} (ID: {factor.factor_id})")
                        print(f"  公式: {factor.formula}")
                        print(f"  所需字段: {', '.join(factor.required_fields)}")
                        print(f"  原始复杂度: {factor.ast.get_complexity()}")
                        print(f"  公共子表达式数: {len(factor.common_expressions)}")
                        if factor.common_expressions:
                            for expr in factor.common_expressions:
                                print(f"    {expr.variable_name}: {expr.to_string()} (使用{expr.usage_count}次)")
                        print(f"  优化后AST: {factor.optimized_ast.to_string()}")

            if args.dry_run:
                print(f"\n=== 干运行模式：优化预览 ===")
                total_common = sum(len(f.common_expressions) for f in factors)
                factors_with_opt = sum(1 for f in factors if len(f.common_expressions) > 0)
                print(f"  解析的因子数: {len(factors)}")
                print(f"  可提取的公共子表达式总数: {total_common}")
                print(f"  有优化潜力的因子数: {factors_with_opt}")
                print(f"  优化率: {factors_with_opt/len(factors)*100:.1f}%")
                return 0

            # 生成代码
            print(f"\n=== 开始生成优化代码 ===")
            print(f"输出目录: {args.output_dir}")
            code_generator = TreeBasedCppCodeGenerator(args.output_dir)
            code_generator.generate_factors(factors)

            print("\n✅ 基于树结构的优化代码生成完成！")
            print(f"📁 输出目录: {args.output_dir}")
            print(f"📊 生成因子数量: {len(factors)}")
            print(f"📄 生成文件数量: {len(factors) * 2 + 3}")
            print(f"📈 查看优化报告: {args.output_dir}/tree_optimization_report.json")

        else:
            print("=== 默认运行测试 ===")
            test_tree_based_parser()
            test_factor_optimization()

        return 0

    except Exception as e:
        print(f"❌ 错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
