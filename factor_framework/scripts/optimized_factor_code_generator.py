#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的因子代码生成器 - 支持公共子表达式提取

这个脚本从feature.csv文件读取因子定义，自动生成对应的C++因子类。
主要优化：
1. 提取公共子表达式，避免重复计算
2. 生成更高效的C++代码
3. 自动变量命名和管理

使用方法：
    python optimized_factor_code_generator.py --input feature.csv --output_dir optimized_factors

作者：AI Assistant
版本：2.0.0
"""

import os
import sys
import csv
import re
import argparse
from typing import List, Dict, Tuple, Set, Optional
from pathlib import Path
import json
from collections import defaultdict
import hashlib


class ExpressionNode:
    """表达式树节点"""
    def __init__(self, value: str, children: List['ExpressionNode'] = None):
        self.value = value
        self.children = children or []
        self.hash_value = None
        self.variable_name = None
        self.is_common = False
        self.usage_count = 0

    def __hash__(self):
        if self.hash_value is None:
            content = self.value + ''.join(str(hash(child)) for child in self.children)
            self.hash_value = hash(content)
        return self.hash_value

    def __eq__(self, other):
        if not isinstance(other, ExpressionNode):
            return False
        return (self.value == other.value and
                len(self.children) == len(other.children) and
                all(c1 == c2 for c1, c2 in zip(self.children, other.children)))

    def to_string(self) -> str:
        """将表达式树转换为字符串"""
        if not self.children:
            return self.value

        if self.value in ['(', ')']:
            return self.value

        # 函数调用格式
        if self.children:
            args = ','.join(child.to_string() for child in self.children)
            return f"{self.value}({args})"

        return self.value

    def get_complexity(self) -> int:
        """计算表达式复杂度"""
        if not self.children:
            return 1
        return 1 + sum(child.get_complexity() for child in self.children)


class ExpressionParser:
    """表达式解析器 - 将字符串解析为表达式树"""

    def __init__(self):
        # 操作符优先级
        self.operators = {
            '+': 1, '-': 1,
            '*': 2, '/': 2,
            '(': 0, ')': 0
        }

        # 函数列表
        self.functions = {
            'ts_Delay', 'ts_Mean', 'ts_Stdev', 'ts_Delta', 'ts_Min', 'ts_Max',
            'ts_Corr', 'ts_Sum', 'ts_Rank', 'ts_Scale', 'ts_Regression',
            'pn_Rank', 'pn_Mean', 'pn_TransStd',
            'Tot_Mean', 'Tot_Sum', 'Tot_Stdev', 'Tot_Rank', 'Tot_ArgMax', 'Tot_ArgMin',
            'IfThen', 'Abs', 'Log', 'Sqrt', 'Equal', 'getNan', 'Power'
        }

    def tokenize(self, formula: str) -> List[str]:
        """将公式分词"""
        tokens = []
        i = 0
        while i < len(formula):
            if formula[i].isspace():
                i += 1
                continue

            # 处理函数名
            if formula[i].isalpha():
                j = i
                while j < len(formula) and (formula[j].isalnum() or formula[j] == '_'):
                    j += 1
                tokens.append(formula[i:j])
                i = j
            # 处理数字
            elif formula[i].isdigit() or formula[i] == '.':
                j = i
                while j < len(formula) and (formula[j].isdigit() or formula[j] == '.'):
                    j += 1
                tokens.append(formula[i:j])
                i = j
            # 处理操作符和括号
            elif formula[i] in '+-*/(),':
                tokens.append(formula[i])
                i += 1
            else:
                i += 1

        return tokens

    def parse_function_call(self, tokens: List[str], start: int) -> Tuple[ExpressionNode, int]:
        """解析函数调用"""
        func_name = tokens[start]
        if start + 1 >= len(tokens) or tokens[start + 1] != '(':
            # 不是函数调用，是变量
            return ExpressionNode(func_name), start + 1

        # 解析函数参数
        args = []
        i = start + 2  # 跳过函数名和左括号
        paren_count = 1
        arg_start = i

        while i < len(tokens) and paren_count > 0:
            if tokens[i] == '(':
                paren_count += 1
            elif tokens[i] == ')':
                paren_count -= 1
                if paren_count == 0:
                    # 解析最后一个参数
                    if i > arg_start:
                        arg_tokens = tokens[arg_start:i]
                        if arg_tokens:
                            arg_node = self.parse_expression(arg_tokens)
                            args.append(arg_node)
                    break
            elif tokens[i] == ',' and paren_count == 1:
                # 解析一个参数
                arg_tokens = tokens[arg_start:i]
                if arg_tokens:
                    arg_node = self.parse_expression(arg_tokens)
                    args.append(arg_node)
                arg_start = i + 1
            i += 1

        return ExpressionNode(func_name, args), i + 1

    def parse_expression(self, tokens: List[str]) -> ExpressionNode:
        """解析表达式为表达式树"""
        if not tokens:
            return ExpressionNode("")

        if len(tokens) == 1:
            return ExpressionNode(tokens[0])

        # 处理括号表达式
        if tokens[0] == '(' and tokens[-1] == ')':
            return self.parse_expression(tokens[1:-1])

        # 查找主操作符（优先级最低的，从右到左）
        min_precedence = float('inf')
        main_op_pos = -1
        paren_count = 0

        # 从右到左扫描，确保右结合性
        for i in range(len(tokens) - 1, -1, -1):
            token = tokens[i]
            if token == ')':
                paren_count += 1
            elif token == '(':
                paren_count -= 1
            elif token in self.operators and paren_count == 0:
                precedence = self.operators[token]
                if precedence < min_precedence:
                    min_precedence = precedence
                    main_op_pos = i

        # 如果找到主操作符，分割表达式
        if main_op_pos != -1:
            left_tokens = tokens[:main_op_pos]
            right_tokens = tokens[main_op_pos + 1:]
            op = tokens[main_op_pos]

            left_node = self.parse_expression(left_tokens) if left_tokens else None
            right_node = self.parse_expression(right_tokens) if right_tokens else None

            children = [child for child in [left_node, right_node] if child is not None]
            return ExpressionNode(op, children)

        # 检查是否是函数调用
        if tokens[0] in self.functions and len(tokens) > 1 and tokens[1] == '(':
            node, end_pos = self.parse_function_call(tokens, 0)
            # 如果还有剩余的tokens，说明函数调用后面还有其他内容
            if end_pos < len(tokens):
                remaining_tokens = tokens[end_pos:]
                # 如果剩余部分是操作符，需要继续解析
                if remaining_tokens and remaining_tokens[0] in self.operators:
                    # 重新解析整个表达式，将函数调用作为一个整体
                    return self.parse_expression(tokens)
            return node

        # 默认情况：连接所有token
        return ExpressionNode(''.join(tokens))


class CommonSubexpressionExtractor:
    """公共子表达式提取器"""

    def __init__(self, min_complexity: int = 3, min_usage: int = 2):
        self.min_complexity = min_complexity  # 最小复杂度阈值
        self.min_usage = min_usage  # 最小使用次数阈值
        self.expression_count = defaultdict(int)
        self.expression_nodes = {}
        self.variable_counter = 0

    def collect_subexpressions(self, node: ExpressionNode):
        """收集所有子表达式"""
        # 递归收集子表达式
        for child in node.children:
            self.collect_subexpressions(child)

        # 只收集复杂度足够的表达式，并且不是简单的操作符
        if (node.get_complexity() >= self.min_complexity and
            node.value not in ['+', '-', '*', '/'] and
            len(node.children) > 0):  # 确保不是叶子节点
            node_key = node.to_string()
            self.expression_count[node_key] += 1
            self.expression_nodes[node_key] = node

    def identify_common_expressions(self) -> List[ExpressionNode]:
        """识别公共子表达式"""
        common_expressions = []

        for expr_str, count in self.expression_count.items():
            if count >= self.min_usage:
                node = self.expression_nodes[expr_str]
                node.is_common = True
                node.usage_count = count
                node.variable_name = f"p{self.variable_counter}"
                self.variable_counter += 1
                common_expressions.append(node)

        # 按复杂度排序，复杂的表达式先提取
        common_expressions.sort(key=lambda x: x.get_complexity(), reverse=True)
        return common_expressions

    def replace_with_variables(self, node: ExpressionNode, common_map: Dict[str, str]) -> ExpressionNode:
        """用变量替换公共子表达式"""
        node_str = node.to_string()

        # 如果当前节点是公共表达式，替换为变量
        if node_str in common_map:
            return ExpressionNode(common_map[node_str])

        # 递归处理子节点，但要先检查子节点是否可以被替换
        new_children = []
        for child in node.children:
            child_str = child.to_string()
            if child_str in common_map:
                # 子节点是公共表达式，直接替换为变量
                new_children.append(ExpressionNode(common_map[child_str]))
            else:
                # 递归处理子节点
                new_child = self.replace_with_variables(child, common_map)
                new_children.append(new_child)

        return ExpressionNode(node.value, new_children)


class OptimizedFactorInfo:
    """优化的因子信息类"""
    def __init__(self, factor_id: int, factor_name: str, formula: str):
        self.factor_id = factor_id
        self.factor_name = factor_name
        self.formula = formula
        self.required_fields = self._extract_required_fields()
        self.class_name = self._generate_class_name()

        # 解析和优化表达式
        self.parser = ExpressionParser()
        self.extractor = CommonSubexpressionExtractor()
        self.expression_tree = None
        self.common_expressions = []
        self.optimized_tree = None
        self._parse_and_optimize()

    def _extract_required_fields(self) -> Set[str]:
        """从公式中提取所需的数据字段"""
        market_fields = {'Close', 'Open', 'High', 'Low', 'Volume', 'Amount', 'VWAP'}
        required = set()

        for field in market_fields:
            if field in self.formula:
                required.add(field)

        return required

    def _generate_class_name(self) -> str:
        """生成C++类名"""
        class_name = self.factor_name.replace('_', '')
        class_name = class_name[0].upper() + class_name[1:] if class_name else 'Factor'
        return f"Factor{class_name}"

    def _parse_and_optimize(self):
        """解析和优化表达式"""
        # 分词并解析
        tokens = self.parser.tokenize(self.formula)
        self.expression_tree = self.parser.parse_expression(tokens)

        # 收集子表达式
        self.extractor.collect_subexpressions(self.expression_tree)

        # 识别公共表达式
        self.common_expressions = self.extractor.identify_common_expressions()

        # 创建替换映射
        common_map = {}
        for expr in self.common_expressions:
            common_map[expr.to_string()] = expr.variable_name

        # 替换公共表达式
        self.optimized_tree = self.extractor.replace_with_variables(self.expression_tree, common_map)


class OptimizedCppCodeGenerator:
    """优化的C++代码生成器"""

    def __init__(self, output_dir: str):
        self.output_dir = Path(output_dir)
        self.operator_mapping = {
            'ts_Delay': 'feature_operators::ts_Delay',
            'ts_Mean': 'feature_operators::ts_Mean',
            'ts_Stdev': 'feature_operators::ts_Stdev',
            'ts_Delta': 'feature_operators::ts_Delta',
            'ts_Min': 'feature_operators::ts_Min',
            'ts_Max': 'feature_operators::ts_Max',
            'ts_Corr': 'feature_operators::ts_Corr',
            'ts_Sum': 'feature_operators::ts_Sum',
            'ts_Rank': 'feature_operators::ts_Rank',
            'ts_Scale': 'feature_operators::ts_Scale',
            'ts_Regression': 'feature_operators::ts_Regression',
            'pn_Rank': 'feature_operators::pn_Rank',
            'pn_Mean': 'feature_operators::pn_Mean',
            'pn_TransStd': 'feature_operators::pn_TransStd',
            'Tot_Mean': 'feature_operators::Tot_Mean',
            'Tot_Sum': 'feature_operators::Tot_Sum',
            'Tot_Stdev': 'feature_operators::Tot_Stdev',
            'Tot_Rank': 'feature_operators::Tot_Rank',
            'Tot_ArgMax': 'feature_operators::Tot_ArgMax',
            'Tot_ArgMin': 'feature_operators::Tot_ArgMin',
            'IfThen': 'feature_operators::IfThen',
            'Abs': 'feature_operators::Abs',
            'Log': 'feature_operators::Log',
            'Sqrt': 'feature_operators::Sqrt',
            'Equal': 'feature_operators::Equal',
            'getNan': 'feature_operators::getNan',
            'Power': 'feature_operators::Power',
        }

        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        (self.output_dir / "include").mkdir(exist_ok=True)
        (self.output_dir / "src").mkdir(exist_ok=True)

    def node_to_cpp(self, node: ExpressionNode) -> str:
        """将表达式节点转换为C++代码"""
        if not node.children:
            # 叶子节点
            if node.value in ['Close', 'Open', 'High', 'Low', 'Volume', 'Amount', 'VWAP']:
                return f'get_field(data_map, "{node.value}")'
            return node.value

        # 操作符节点
        if node.value in ['+', '-', '*', '/']:
            if len(node.children) == 2:
                left = self.node_to_cpp(node.children[0])
                right = self.node_to_cpp(node.children[1])
                return f"({left} {node.value} {right})"

        # 函数调用
        if node.value in self.operator_mapping:
            cpp_func = self.operator_mapping[node.value]
            args = [self.node_to_cpp(child) for child in node.children]
            return f"{cpp_func}({','.join(args)})"

        return node.value

    def generate_optimized_calculation(self, factor: OptimizedFactorInfo) -> str:
        """生成优化的计算代码"""
        lines = []

        # 生成公共子表达式
        for i, expr in enumerate(factor.common_expressions):
            cpp_code = self.node_to_cpp(expr)
            lines.append(f"        auto {expr.variable_name} = {cpp_code};")

        # 生成最终结果
        final_code = self.node_to_cpp(factor.optimized_tree)
        lines.append(f"        auto result = {final_code};")

        return '\n'.join(lines)

    def generate_factor_header(self, factor: OptimizedFactorInfo) -> str:
        """生成因子头文件"""
        template = f'''#ifndef GENERATED_FACTORS_{factor.class_name.upper()}_HPP
#define GENERATED_FACTORS_{factor.class_name.upper()}_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {{

/**
 * {factor.factor_name} 因子 (优化版本)
 * ID: {factor.factor_id}
 * 原始公式: {factor.formula}
 * 公共子表达式数量: {len(factor.common_expressions)}
 */
class {factor.class_name} : public factor_framework::factor_base {{
public:
    /**
     * 构造函数
     */
    {factor.class_name}(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值 (优化版本)
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
}};

}} // namespace generated_factors

#endif // GENERATED_FACTORS_{factor.class_name.upper()}_HPP'''

        return template

    def generate_factor_implementation(self, factor: OptimizedFactorInfo) -> str:
        """生成因子实现文件"""
        calculation_code = self.generate_optimized_calculation(factor)
        required_fields_str = ', '.join(f'"{field}"' for field in factor.required_fields)

        # 生成优化信息注释
        optimization_info = []
        if factor.common_expressions:
            optimization_info.append(" * 优化信息:")
            for expr in factor.common_expressions:
                optimization_info.append(f" *   {expr.variable_name}: {expr.to_string()} (使用{expr.usage_count}次)")

        template = f'''#include "generated_factors/{factor.class_name.lower()}.hpp"
#include <stdexcept>

namespace generated_factors {{

{factor.class_name}::{factor.class_name}(int factor_id, const std::string& factor_name, const std::string& formula)
    : factor_base(factor_id, factor_name, formula) {{
    // 注册因子到管理器
    register_factor();
}}

/**
 * 计算因子值 - 优化版本
 * 原始公式: {factor.formula}
{chr(10).join(optimization_info)}
 */
feature_operators::DataFrame {factor.class_name}::calculate(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) {{

    // 验证输入数据
    if (!validate_input(data_map)) {{
        throw std::runtime_error("Invalid input data for factor " + get_name());
    }}

    try {{
        // 执行优化的因子计算
{calculation_code}
        return result;
    }} catch (const std::exception& e) {{
        throw std::runtime_error("Error calculating factor " + get_name() + ": " + e.what());
    }}
}}

std::vector<std::string> {factor.class_name}::get_required_fields() const {{
    return {{{required_fields_str}}};
}}

}} // namespace generated_factors'''

        return template

    def generate_factors(self, factors: List[OptimizedFactorInfo]):
        """生成所有因子代码"""
        print(f"开始生成 {len(factors)} 个优化因子的C++代码...")

        # 统计优化效果
        total_original_complexity = 0
        total_optimized_complexity = 0
        total_common_expressions = 0

        for i, factor in enumerate(factors):
            print(f"生成因子 {i+1}/{len(factors)}: {factor.factor_name}")

            # 统计复杂度
            original_complexity = factor.expression_tree.get_complexity()
            optimized_complexity = factor.optimized_tree.get_complexity() + sum(expr.get_complexity() for expr in factor.common_expressions)
            common_count = len(factor.common_expressions)

            total_original_complexity += original_complexity
            total_optimized_complexity += optimized_complexity
            total_common_expressions += common_count

            if common_count > 0:
                print(f"  优化: 提取了{common_count}个公共子表达式")

            # 生成头文件
            header_content = self.generate_factor_header(factor)
            header_file = self.output_dir / "include" / "generated_factors" / f"{factor.class_name.lower()}.hpp"
            header_file.parent.mkdir(parents=True, exist_ok=True)
            header_file.write_text(header_content, encoding='utf-8')

            # 生成实现文件
            impl_content = self.generate_factor_implementation(factor)
            impl_file = self.output_dir / "src" / f"{factor.class_name.lower()}.cpp"
            impl_file.write_text(impl_content, encoding='utf-8')

        # 输出优化统计
        print(f"\n优化统计:")
        print(f"  总因子数: {len(factors)}")
        print(f"  提取的公共子表达式总数: {total_common_expressions}")
        print(f"  原始总复杂度: {total_original_complexity}")
        print(f"  优化后总复杂度: {total_optimized_complexity}")
        if total_original_complexity > 0:
            reduction = (total_original_complexity - total_optimized_complexity) / total_original_complexity * 100
            print(f"  复杂度减少: {reduction:.1f}%")

        # 生成其他文件
        self.generate_all_factors_header(factors)
        self.generate_all_factors_implementation(factors)
        self.generate_cmake_file(factors)
        self.generate_optimization_report(factors)

        print(f"代码生成完成！输出目录: {self.output_dir}")

    def generate_all_factors_header(self, factors: List[OptimizedFactorInfo]):
        """生成包含所有因子的头文件"""
        includes = []
        for factor in factors:
            includes.append(f'#include "generated_factors/{factor.class_name.lower()}.hpp"')

        template = f'''#ifndef GENERATED_FACTORS_ALL_FACTORS_HPP
#define GENERATED_FACTORS_ALL_FACTORS_HPP

/**
 * 自动生成的优化因子头文件
 * 包含所有从feature.csv生成的优化因子类
 *
 * 生成的因子数量: {len(factors)}
 * 优化特性: 公共子表达式提取
 */

// 包含因子框架
#include "factor_framework/factor_framework.hpp"

// 包含所有生成的优化因子
{chr(10).join(includes)}

namespace generated_factors {{

/**
 * 获取所有生成的因子名称
 */
std::vector<std::string> get_all_generated_factor_names();

/**
 * 获取所有生成的因子ID
 */
std::vector<int> get_all_generated_factor_ids();

/**
 * 初始化所有生成的因子（确保注册）
 */
void initialize_all_factors();

/**
 * 批量注册所有生成的因子到factor_manager
 * @param manager 因子管理器
 * @return 成功注册的因子数量
 */
int register_all_factors_to_manager(std::shared_ptr<factor_framework::factor_manager> manager);

}} // namespace generated_factors

#endif // GENERATED_FACTORS_ALL_FACTORS_HPP'''

        all_header_file = self.output_dir / "include" / "generated_factors" / "all_factors.hpp"
        all_header_file.write_text(template, encoding='utf-8')

    def generate_all_factors_implementation(self, factors: List[OptimizedFactorInfo]):
        """生成包含所有因子的实现文件"""
        factor_names = ', '.join(f'"{factor.factor_name}"' for factor in factors)
        factor_ids = ', '.join(str(factor.factor_id) for factor in factors)

        template = f'''#include "generated_factors/all_factors.hpp"
#include <iostream>
#include <algorithm>

namespace generated_factors {{

std::vector<std::string> get_all_generated_factor_names() {{
    return {{{factor_names}}};
}}

std::vector<int> get_all_generated_factor_ids() {{
    return {{{factor_ids}}};
}}

void initialize_all_factors() {{
    static bool initialized = false;
    if (!initialized) {{
        std::cout << "✓ Optimized generated factors library initialized" << std::endl;
        std::cout << "✓ Available factors: " << {len(factors)} << std::endl;

        auto factor_names = get_all_generated_factor_names();
        for (const auto& factor_name : factor_names) {{
            std::cout << "  - " << factor_name << std::endl;
        }}

        initialized = true;
    }}
}}

int register_all_factors_to_manager(std::shared_ptr<factor_framework::factor_manager> manager) {{
    if (!manager) {{
        std::cerr << "Error: factor_manager is null" << std::endl;
        return 0;
    }}

    int registered_count = 0;
    auto factor_names = get_all_generated_factor_names();
    auto factor_ids = get_all_generated_factor_ids();

    for (size_t i = 0; i < factor_names.size(); ++i) {{
        try {{
            // 这里需要根据具体的因子注册机制来实现
            // 暂时使用占位符
            registered_count++;
        }} catch (const std::exception& e) {{
            std::cerr << "Error registering factor " << factor_names[i] << ": " << e.what() << std::endl;
        }}
    }}

    std::cout << "✓ Registered " << registered_count << " optimized factors to manager" << std::endl;
    return registered_count;
}}

}} // namespace generated_factors'''

        all_impl_file = self.output_dir / "src" / "all_factors.cpp"
        all_impl_file.write_text(template, encoding='utf-8')

    def generate_cmake_file(self, factors: List[OptimizedFactorInfo]):
        """生成CMake构建文件"""
        source_files = []
        for factor in factors:
            source_files.append(f"src/{factor.class_name.lower()}.cpp")

        template = f'''# 自动生成的优化因子CMake文件
cmake_minimum_required(VERSION 3.10)

# 添加生成的优化因子源文件
set(OPTIMIZED_FACTOR_SOURCES
    src/all_factors.cpp
{chr(10).join(f"    {src}" for src in source_files)}
)

# 添加生成的因子头文件目录
set(OPTIMIZED_FACTOR_INCLUDE_DIRS
    include
)

# 创建优化因子库
add_library(optimized_factors STATIC ${{OPTIMIZED_FACTOR_SOURCES}})

# 设置包含目录
target_include_directories(optimized_factors PUBLIC
    ${{OPTIMIZED_FACTOR_INCLUDE_DIRS}}
    ${{CMAKE_SOURCE_DIR}}/include
)

# 链接feature_operators库
target_link_libraries(optimized_factors
    feature_ops_lib
)

# 设置编译选项
target_compile_features(optimized_factors PUBLIC cxx_std_17)
target_compile_options(optimized_factors PRIVATE
    $<$<CXX_COMPILER_ID:GNU>:-Wall -Wextra -O3>
    $<$<CXX_COMPILER_ID:Clang>:-Wall -Wextra -O3>
    $<$<CXX_COMPILER_ID:MSVC>:/W4 /O2>
)

# 安装规则
install(TARGETS optimized_factors
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(DIRECTORY include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.hpp"
)'''

        cmake_file = self.output_dir / "CMakeLists.txt"
        cmake_file.write_text(template, encoding='utf-8')

    def generate_optimization_report(self, factors: List[OptimizedFactorInfo]):
        """生成优化报告"""
        report_data = []
        total_original = 0
        total_optimized = 0
        total_common = 0

        for factor in factors:
            original_complexity = factor.expression_tree.get_complexity()
            optimized_complexity = factor.optimized_tree.get_complexity() + sum(expr.get_complexity() for expr in factor.common_expressions)
            common_count = len(factor.common_expressions)

            total_original += original_complexity
            total_optimized += optimized_complexity
            total_common += common_count

            factor_data = {
                "id": factor.factor_id,
                "name": factor.factor_name,
                "formula": factor.formula,
                "original_complexity": original_complexity,
                "optimized_complexity": optimized_complexity,
                "common_expressions_count": common_count,
                "common_expressions": [
                    {
                        "variable": expr.variable_name,
                        "expression": expr.to_string(),
                        "usage_count": expr.usage_count,
                        "complexity": expr.get_complexity()
                    }
                    for expr in factor.common_expressions
                ],
                "reduction_percentage": ((original_complexity - optimized_complexity) / original_complexity * 100) if original_complexity > 0 else 0
            }
            report_data.append(factor_data)

        # 生成总结报告
        summary = {
            "total_factors": len(factors),
            "total_original_complexity": total_original,
            "total_optimized_complexity": total_optimized,
            "total_common_expressions": total_common,
            "overall_reduction_percentage": ((total_original - total_optimized) / total_original * 100) if total_original > 0 else 0,
            "factors_with_optimization": sum(1 for f in factors if len(f.common_expressions) > 0),
            "average_common_expressions_per_factor": total_common / len(factors) if len(factors) > 0 else 0
        }

        report = {
            "summary": summary,
            "factors": report_data
        }

        # 保存JSON报告
        report_file = self.output_dir / "optimization_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        # 生成可读的文本报告
        text_report = f"""# 因子优化报告

## 总体统计
- 总因子数: {summary['total_factors']}
- 原始总复杂度: {summary['total_original_complexity']}
- 优化后总复杂度: {summary['total_optimized_complexity']}
- 总体复杂度减少: {summary['overall_reduction_percentage']:.1f}%
- 提取的公共子表达式总数: {summary['total_common_expressions']}
- 有优化的因子数: {summary['factors_with_optimization']}
- 平均每个因子的公共表达式数: {summary['average_common_expressions_per_factor']:.1f}

## 详细因子信息
"""

        for factor_data in report_data:
            if factor_data['common_expressions_count'] > 0:
                text_report += f"""
### {factor_data['name']} (ID: {factor_data['id']})
- 原始公式: {factor_data['formula']}
- 原始复杂度: {factor_data['original_complexity']}
- 优化后复杂度: {factor_data['optimized_complexity']}
- 复杂度减少: {factor_data['reduction_percentage']:.1f}%
- 公共子表达式数: {factor_data['common_expressions_count']}

公共子表达式:
"""
                for expr in factor_data['common_expressions']:
                    text_report += f"  - {expr['variable']}: {expr['expression']} (使用{expr['usage_count']}次, 复杂度{expr['complexity']})\n"

        text_report_file = self.output_dir / "optimization_report.md"
        text_report_file.write_text(text_report, encoding='utf-8')


class OptimizedFactorCSVParser:
    """优化的因子CSV文件解析器"""

    def __init__(self, csv_file_path: str):
        self.csv_file_path = csv_file_path

    def parse(self) -> List[OptimizedFactorInfo]:
        """解析CSV文件，返回优化的因子信息列表"""
        factors = []

        try:
            with open(self.csv_file_path, 'r', encoding='utf-8') as f:
                # 跳过第一行（标题行）
                next(f)

                for line_num, line in enumerate(f, start=2):
                    line = line.strip()
                    if not line:
                        continue

                    try:
                        factor = self._parse_line(line)
                        if factor:
                            factors.append(factor)
                    except Exception as e:
                        print(f"警告：解析第{line_num}行时出错: {e}")
                        continue

        except FileNotFoundError:
            raise FileNotFoundError(f"找不到CSV文件: {self.csv_file_path}")
        except Exception as e:
            raise RuntimeError(f"解析CSV文件时出错: {e}")

        print(f"成功解析 {len(factors)} 个因子")
        return factors

    def _parse_line(self, line: str) -> OptimizedFactorInfo:
        """解析CSV行"""
        # 简单的CSV解析（处理带引号的字段）
        parts = []
        current_part = ""
        in_quotes = False

        for char in line:
            if char == '"':
                in_quotes = not in_quotes
            elif char == ',' and not in_quotes:
                parts.append(current_part.strip())
                current_part = ""
            else:
                current_part += char

        if current_part:
            parts.append(current_part.strip())

        if len(parts) < 3:
            raise ValueError(f"CSV行格式错误，期望至少3列: {line}")

        # 解析字段
        factor_id = int(parts[0]) if parts[0].isdigit() else 0
        factor_name = parts[1].strip('"')
        formula = parts[2].strip('"')

        return OptimizedFactorInfo(factor_id, factor_name, formula)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="优化的因子代码生成器 - 支持公共子表达式提取",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python optimized_factor_code_generator.py --input feature.csv --output_dir optimized_factors
  python optimized_factor_code_generator.py -i feature.csv -o optimized_factors --verbose
  python optimized_factor_code_generator.py --input feature.csv --output_dir optimized_factors --min_complexity 5 --min_usage 3
        """
    )

    parser.add_argument(
        '--input', '-i',
        required=True,
        help='输入的因子CSV文件路径'
    )

    parser.add_argument(
        '--output_dir', '-o',
        default='optimized_factors',
        help='输出目录路径（默认: optimized_factors）'
    )

    parser.add_argument(
        '--min_complexity',
        type=int,
        default=3,
        help='公共子表达式的最小复杂度阈值（默认: 3）'
    )

    parser.add_argument(
        '--min_usage',
        type=int,
        default=2,
        help='公共子表达式的最小使用次数阈值（默认: 2）'
    )

    parser.add_argument(
        '--filter', '-f',
        help='因子名称过滤器（支持通配符，如 "p1_*"）'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='启用详细输出'
    )

    parser.add_argument(
        '--dry_run',
        action='store_true',
        help='只解析不生成代码（用于验证）'
    )

    parser.add_argument(
        '--max_factors',
        type=int,
        help='限制生成的因子数量（用于测试）'
    )

    args = parser.parse_args()

    try:
        # 解析CSV文件
        print(f"解析因子配置文件: {args.input}")
        csv_parser = OptimizedFactorCSVParser(args.input)
        factors = csv_parser.parse()

        if not factors:
            print("错误：没有找到有效的因子定义")
            return 1

        # 应用过滤器
        if args.filter:
            import fnmatch
            filtered_factors = []
            for factor in factors:
                if fnmatch.fnmatch(factor.factor_name, args.filter):
                    filtered_factors.append(factor)
            factors = filtered_factors
            print(f"应用过滤器 '{args.filter}' 后，剩余 {len(factors)} 个因子")

        # 限制因子数量
        if args.max_factors and len(factors) > args.max_factors:
            factors = factors[:args.max_factors]
            print(f"限制因子数量为 {args.max_factors}")

        # 设置优化参数
        for factor in factors:
            factor.extractor.min_complexity = args.min_complexity
            factor.extractor.min_usage = args.min_usage
            # 重新解析和优化
            factor._parse_and_optimize()

        # 打印因子信息
        if args.verbose:
            print("\n因子列表:")
            for factor in factors:
                print(f"  ID: {factor.factor_id}, 名称: {factor.factor_name}")
                print(f"    公式: {factor.formula}")
                print(f"    所需字段: {', '.join(factor.required_fields)}")
                print(f"    类名: {factor.class_name}")
                print(f"    公共子表达式数: {len(factor.common_expressions)}")
                if factor.common_expressions:
                    for expr in factor.common_expressions:
                        print(f"      {expr.variable_name}: {expr.to_string()} (使用{expr.usage_count}次)")
                print()

        if args.dry_run:
            print(f"干运行模式：解析了 {len(factors)} 个因子，未生成代码")
            # 输出优化统计
            total_common = sum(len(f.common_expressions) for f in factors)
            factors_with_opt = sum(1 for f in factors if len(f.common_expressions) > 0)
            print(f"优化预览：")
            print(f"  可提取的公共子表达式总数: {total_common}")
            print(f"  有优化潜力的因子数: {factors_with_opt}")
            return 0

        # 生成代码
        print(f"\n开始生成优化代码到目录: {args.output_dir}")
        code_generator = OptimizedCppCodeGenerator(args.output_dir)
        code_generator.generate_factors(factors)

        print("\n✅ 优化代码生成完成！")
        print(f"📁 输出目录: {args.output_dir}")
        print(f"📊 生成因子数量: {len(factors)}")
        print(f"📄 生成文件数量: {len(factors) * 2 + 6}")  # 每个因子2个文件 + 6个公共文件
        print(f"📈 查看优化报告: {args.output_dir}/optimization_report.md")

        return 0

    except Exception as e:
        print(f"❌ 错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())